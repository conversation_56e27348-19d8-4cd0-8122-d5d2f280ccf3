from odoo import models, fields, api
from odoo.exceptions import UserError

class CustomerStatementWizard(models.TransientModel):
    _name = 'customer.statement.wizard'
    _description = 'Customer Statement Wizard'

    partner_id = fields.Many2one('res.partner', string='Customer', required=True, domain=[('customer_rank', '>', 0)])
    date_from = fields.Date(string='From Date')
    date_to = fields.Date(string='To Date')

    def action_view_statement(self):
        """Generate and view customer statement"""
        if not self.partner_id:
            raise UserError('Please select a customer.')

        # Generate statement lines
        statement_model = self.env['customer.statement.line']
        statement_model.generate_statement_lines(
            self.partner_id.id,
            self.date_from,
            self.date_to
        )

        return {
            'type': 'ir.actions.act_window',
            'name': f'Customer Statement - {self.partner_id.name}',
            'res_model': 'customer.statement.line',
            'view_mode': 'tree,form',
            'domain': [('partner_id', '=', self.partner_id.id)],
            'context': {
                'default_partner_id': self.partner_id.id,
                'search_default_partner_id': self.partner_id.id,
            },
        }

    def action_print_pdf(self):
        """Print customer statement as PDF"""
        if not self.partner_id:
            raise UserError('Please select a customer.')

        # Generate statement lines first
        statement_model = self.env['customer.statement.line']
        statement_model.generate_statement_lines(
            self.partner_id.id,
            self.date_from,
            self.date_to
        )

        # Return report action (will be implemented later)
        return {
            'type': 'ir.actions.report',
            'report_name': 'customer_account_statement.customer_statement_report',
            'report_type': 'qweb-pdf',
            'data': {
                'partner_id': self.partner_id.id,
                'date_from': self.date_from,
                'date_to': self.date_to,
            },
            'context': self.env.context,
        }