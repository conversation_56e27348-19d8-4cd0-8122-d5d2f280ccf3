from odoo import models, fields, api

class CustomerStatementWizard(models.TransientModel):
    _name = 'customer.statement.wizard'
    _description = 'Customer Statement Wizard'

    partner_id = fields.Many2one('res.partner', string='Customer', required=True, domain=[('customer_rank', '>', 0)])
    date_from = fields.Date(string='From')
    date_to = fields.Date(string='To')

    def action_view_statement(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Customer Statement',
            'res_model': 'customer.statement.line',
            'view_mode': 'tree',
            'domain': [('partner_id', '=', self.partner_id.id)],
        }

    def action_print_pdf(self):
        return self.env.ref('customer_account_statement.customer_statement_report_action').report_action(self)