<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="customer_statement_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="oe_structure"/>
                        
                        <!-- Header -->
                        <div class="row">
                            <div class="col-12">
                                <h2>Customer Account Statement</h2>
                            </div>
                        </div>
                        
                        <!-- Customer Info -->
                        <div class="row mt-4">
                            <div class="col-6">
                                <strong>Customer:</strong> <span t-field="doc.partner_id.name"/><br/>
                                <span t-field="doc.partner_id.street"/><br/>
                                <span t-field="doc.partner_id.city"/> <span t-field="doc.partner_id.zip"/><br/>
                                <span t-field="doc.partner_id.country_id.name"/>
                            </div>
                            <div class="col-6">
                                <strong>Statement Date:</strong> <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d')"/><br/>
                                <t t-if="doc.date_from">
                                    <strong>From:</strong> <span t-field="doc.date_from"/><br/>
                                </t>
                                <t t-if="doc.date_to">
                                    <strong>To:</strong> <span t-field="doc.date_to"/><br/>
                                </t>
                            </div>
                        </div>
                        
                        <!-- Statement Lines -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Description</th>
                                            <th class="text-right">Debit</th>
                                            <th class="text-right">Credit</th>
                                            <th class="text-right">Balance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-set="statement_lines" t-value="env['customer.statement.line'].search([('partner_id', '=', doc.partner_id.id)], order='date, id')"/>
                                        <tr t-foreach="statement_lines" t-as="line">
                                            <td><span t-field="line.date"/></td>
                                            <td><span t-field="line.description"/></td>
                                            <td class="text-right">
                                                <span t-if="line.debit" t-field="line.debit" t-options="{'widget': 'monetary'}"/>
                                            </td>
                                            <td class="text-right">
                                                <span t-if="line.credit" t-field="line.credit" t-options="{'widget': 'monetary'}"/>
                                            </td>
                                            <td class="text-right">
                                                <span t-field="line.balance" t-options="{'widget': 'monetary'}"/>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Summary -->
                        <div class="row mt-4">
                            <div class="col-6 offset-6">
                                <t t-set="final_balance" t-value="statement_lines[-1].balance if statement_lines else 0"/>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Final Balance:</strong></td>
                                        <td class="text-right">
                                            <strong><span t-esc="final_balance" t-options="{'widget': 'monetary'}"/></strong>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="oe_structure"/>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
