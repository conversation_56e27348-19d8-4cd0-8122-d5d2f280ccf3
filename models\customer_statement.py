from odoo import models, fields, api
from datetime import datetime

class CustomerStatementLine(models.Model):
    _name = 'customer.statement.line'
    _description = 'Customer Statement Line'

    date = fields.Date(string='Date')
    description = fields.Char(string='Description')
    debit = fields.Float(string='Debit')
    credit = fields.Float(string='Credit')
    balance = fields.Float(string='Balance')
    partner_id = fields.Many2one('res.partner', string='Customer')