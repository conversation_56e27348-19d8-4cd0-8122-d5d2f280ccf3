from odoo import models, fields, api
from datetime import datetime

class CustomerStatementLine(models.Model):
    _name = 'customer.statement.line'
    _description = 'Customer Statement Line'
    _order = 'date desc, id desc'

    date = fields.Date(string='Date', required=True)
    description = fields.Char(string='Description', required=True)
    debit = fields.Float(string='Debit', digits='Account')
    credit = fields.Float(string='Credit', digits='Account')
    balance = fields.Float(string='Balance', digits='Account')
    partner_id = fields.Many2one('res.partner', string='Customer', required=True)
    move_line_id = fields.Many2one('account.move.line', string='Journal Item')

    @api.model
    def generate_statement_lines(self, partner_id, date_from=None, date_to=None):
        """Generate statement lines for a customer based on account move lines"""
        domain = [
            ('partner_id', '=', partner_id),
            ('account_id.account_type', 'in', ['asset_receivable', 'liability_payable']),
        ]

        if date_from:
            domain.append(('date', '>=', date_from))
        if date_to:
            domain.append(('date', '<=', date_to))

        # Clear existing statement lines for this partner
        existing_lines = self.search([('partner_id', '=', partner_id)])
        existing_lines.unlink()

        # Get account move lines
        move_lines = self.env['account.move.line'].search(domain, order='date, id')

        balance = 0.0
        statement_lines = []

        for line in move_lines:
            debit = line.debit if line.account_id.account_type == 'asset_receivable' else line.credit
            credit = line.credit if line.account_id.account_type == 'asset_receivable' else line.debit
            balance += debit - credit

            statement_lines.append({
                'date': line.date,
                'description': line.name or line.move_id.name,
                'debit': debit,
                'credit': credit,
                'balance': balance,
                'partner_id': partner_id,
                'move_line_id': line.id,
            })

        # Create statement lines
        for line_data in statement_lines:
            self.create(line_data)

        return statement_lines