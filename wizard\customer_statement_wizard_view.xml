<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Wizard form view -->
    <record id="view_customer_statement_wizard_form" model="ir.ui.view">
        <field name="name">customer.statement.wizard.form</field>
        <field name="model">customer.statement.wizard</field>
        <field name="arch" type="xml">
            <form string="Customer Statement Wizard">
                <sheet>
                    <group>
                        <group>
                            <field name="partner_id" options="{'no_create': True}"/>
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button name="action_view_statement" string="View Statement" type="object" class="btn-primary"/>
                    <button name="action_print_pdf" string="Print PDF" type="object" class="btn-secondary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for the wizard -->
    <record id="action_customer_statement_wizard" model="ir.actions.act_window">
        <field name="name">Customer Statement Wizard</field>
        <field name="res_model">customer.statement.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu item for the wizard -->
    <menuitem id="menu_customer_statement_wizard"
              name="Generate Statement"
              parent="customer_account_statement.menu_customer_statement_root"
              action="action_customer_statement_wizard"
              sequence="10"/>
</odoo>
