<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree view for Customer Statement Lines -->
    <record id="view_customer_statement_line_tree" model="ir.ui.view">
        <field name="name">customer.statement.line.tree</field>
        <field name="model">customer.statement.line</field>
        <field name="arch" type="xml">
            <tree string="Customer Statement Lines">
                <field name="date"/>
                <field name="description"/>
                <field name="debit"/>
                <field name="credit"/>
                <field name="balance"/>
                <field name="partner_id"/>
            </tree>
        </field>
    </record>

    <!-- Form view for Customer Statement Lines -->
    <record id="view_customer_statement_line_form" model="ir.ui.view">
        <field name="name">customer.statement.line.form</field>
        <field name="model">customer.statement.line</field>
        <field name="arch" type="xml">
            <form string="Customer Statement Line">
                <sheet>
                    <group>
                        <group>
                            <field name="partner_id"/>
                            <field name="date"/>
                            <field name="description"/>
                        </group>
                        <group>
                            <field name="debit"/>
                            <field name="credit"/>
                            <field name="balance"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action for Customer Statement Lines -->
    <record id="action_customer_statement_line" model="ir.actions.act_window">
        <field name="name">Customer Statement Lines</field>
        <field name="res_model">customer.statement.line</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>
