<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Menu for Customer Statement -->
    <menuitem id="menu_customer_statement_root"
              name="Customer Statements"
              sequence="10"/>
    
    <menuitem id="menu_customer_statement_wizard"
              name="Generate Statement"
              parent="menu_customer_statement_root"
              action="action_customer_statement_wizard"
              sequence="10"/>
    
    <!-- Action for the wizard -->
    <record id="action_customer_statement_wizard" model="ir.actions.act_window">
        <field name="name">Customer Statement Wizard</field>
        <field name="res_model">customer.statement.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
