<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Action for the wizard - must be defined before menu -->
    <record id="action_customer_statement_wizard" model="ir.actions.act_window">
        <field name="name">Customer Statement Wizard</field>
        <field name="res_model">customer.statement.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu for Customer Statement -->
    <menuitem id="menu_customer_statement_root"
              name="Customer Statements"
              sequence="10"/>

    <menuitem id="menu_customer_statement_wizard"
              name="Generate Statement"
              parent="menu_customer_statement_root"
              action="action_customer_statement_wizard"
              sequence="10"/>
</odoo>
